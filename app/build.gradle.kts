import java.text.SimpleDateFormat
import java.util.Date

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.parcelize)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildApp.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")

android {
    namespace = "com.flutterup.app"

    defaultConfig {
        applicationId = "com.flutterup.app"
        minSdk = 26
        targetSdk = 36
        versionCode = SimpleDateFormat("yyyyMMdd").format(Date().time).toInt()
        versionName = "1.0.0"

        vectorDrawables {
            useSupportLibrary = true
        }
    }
}

dependencies {
    implementation(project(":base"))
    implementation(project(":network"))
    implementation(project(":tracking"))
    implementation(project(":billinghelper"))
    implementation(project(":gifts"))
    implementation(project(":players"))

    implementation(libs.androidx.splashscreen)
    implementation(libs.googleid)
    implementation(libs.google.credentials)
    implementation(libs.google.auth)
}