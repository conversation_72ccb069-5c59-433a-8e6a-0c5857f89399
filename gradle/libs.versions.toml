[versions]
agp = "8.10.1"
alphaplayer = "1.0.4"
kotlin = "2.1.21"
ksp = "2.1.21-2.0.2"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.1"
material = "1.12.0"
startup = "1.2.0"
splashscreen = "1.0.1"
lifecycle = "2.9.1"
coroutines = "1.10.2"
room = "2.7.2"

#google的
googleid = "1.1.1"
firebase = "33.15.0"
firebase-crashlytics = "3.0.4"
google-services = "4.4.2"
google-services-location = "21.3.0"
google-services-ads-identifier = "18.2.0"
google-billing = "7.1.1"
google-review = "2.0.2"
google-credentials = "1.5.0"
media3 = "1.7.1"

# Compose
compose-bom = "2025.06.01"
activity-compose = "1.10.1"
lifecycle-viewmodel-compose = "2.9.1"
navigation-compose = "2.9.0"

# Dependency Injection
hilt = "2.56.2"

# Network
retrofit = "3.0.0"
okhttp = "4.12.0"
moshi = "1.15.2"

# Image Loading
coil = "3.2.0"

#MMKV
mmkv = "2.2.2"

#adjust
adjust = "5.4.0"
adjust-installreferrer = "2.2"

# Testing
mockk = "1.14.2"
browser = "1.8.0"
webkit = "1.14.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-startup = { group = "androidx.startup", name = "startup-runtime", version.ref = "startup" }
androidx-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }
androidx-room = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

#google
googleid = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "googleid" }
firebase = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase" }
firebase-cloud-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
google-services-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "google-services-location" }
google-services-ads-identifier = { group = "com.google.android.gms", name = "play-services-ads-identifier", version.ref = "google-services-ads-identifier" }
google-billing = { group = "com.android.billingclient", name = "billing-ktx", version.ref = "google-billing" }
google-review = { group = "com.google.android.play", name = "review-ktx", version.ref = "google-review" }
google-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "google-credentials" }
google-auth = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "google-credentials" }

# Compose BOM
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose-bom" }
compose-ui = { group = "androidx.compose.ui", name = "ui" }
compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
compose-material = { group = "androidx.compose.material", name = "material" }
compose-material3 = { group = "androidx.compose.material3", name = "material3" }
compose-activity = { group = "androidx.activity", name = "activity-compose", version.ref = "activity-compose" }
compose-lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle-viewmodel-compose" }
compose-navigation = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation-compose" }

# Hilt
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version = "1.2.0" }

# Network
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-moshi = { group = "com.squareup.retrofit2", name = "converter-moshi", version.ref = "retrofit" }
retrofit-scalars = { group = "com.squareup.retrofit2", name = "converter-scalars", version.ref = "retrofit" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
moshi = { group = "com.squareup.moshi", name = "moshi", version.ref = "moshi" }
moshi-kotlin = { group = "com.squareup.moshi", name = "moshi-kotlin", version.ref = "moshi" }
moshi-codegen = { group = "com.squareup.moshi", name = "moshi-kotlin-codegen", version.ref = "moshi" }

# Image Loading
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-network = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
coil-gif = { group = "io.coil-kt.coil3", name = "coil-gif", version.ref = "coil" }

# Architecture Components
lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycle" }

# Coroutines
coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }
coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }

#MMKV
mmkv = { group = "com.tencent", name = "mmkv", version.ref = "mmkv" }

#adjust
adjust = { group = "com.adjust.sdk", name = "adjust-android", version.ref = "adjust" }
adjust-installreferrer = { group = "com.android.installreferrer", name = "installreferrer", version.ref = "adjust-installreferrer" }

#player
alphaplayer = { module = "com.github.bytedance:AlphaPlayer", version.ref = "alphaplayer" }
media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3" }
media3-exoplayer-dash = { group = "androidx.media3", name = "media3-exoplayer-dash", version.ref = "media3" }
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3" }
media3-ui-compose = { group = "androidx.media3", name = "media3-ui-compose", version.ref = "media3" }


# Testing
mockk = { group = "io.mockk", name = "mockk-android", version.ref = "mockk" }
mockk-agent = { group = "io.mockk", name = "mockk-agent", version.ref = "mockk" }
kotlin-test = { group = "org.jetbrains.kotlin", name = "kotlin-test", version.ref = "kotlin" }
coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutines" }
compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-browser = { group = "androidx.browser", name = "browser", version.ref = "browser" }
androidx-webkit = { group = "androidx.webkit", name = "webkit", version.ref = "webkit" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-parcelize = { id = "kotlin-parcelize" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "google-services" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebase-crashlytics" }

